import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  FormControl,
  Heading,
  Pressable,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useEffect, useState } from "react";
import { Keyboard, TouchableOpacity } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, verticalScale } from "react-native-size-matters";
import LoadingModal from "../components/Loading/LoadingModal";
import OTPInput from "../components/OTP/MaskedOTP";
import AuthService from "../services/Auth/Auth";
import LocalStore from "../utilities/store";
import { useAuth } from "../utilities/AuthContext";

const LoginOTP = (props) => {
  const navigation = useNavigation();
  const { login } = useAuth();
  const [otpCode, setOTPCode] = useState("");
  const [isPinReady, setIsPinReady] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState(props.route.params.number);
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const maximumCodeLength = 4;

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
      setSuccess(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed, success]);

  const handleVerify = async () => {
    try {
      setLoading(true);

      const msisdn = "263" + parseInt(phoneNumber);
      const response = await AuthService.verifyOtpForLogin(msisdn, otpCode);

      if (response.data && response.data.success && response.data.body) {
        await LocalStore.saveData("@username", parseInt(phoneNumber));

        // The token is directly in the body field as a string
        const token = response.data.body;

        // Use the auth context login method
        const loginSuccess = await login(token);

        if (loginSuccess) {
          setSuccessMessage("Login successful");
          navigation.navigate("Tabs");
        } else {
          setFailed(true);
          setErrorMessage("Failed to save login session");
        }
      } else {
        setFailed(true);
        if (response.data && response.data.message) {
          setErrorMessage(response.data.message);
        } else {
          setErrorMessage("Invalid OTP, please try again");
        }
      }

      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status === 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (error.response.status === 401) {
          setErrorMessage("Invalid OTP");
        } else if (
          error.response.status === 403 ||
          error.response.status === 500 ||
          error.response.status === 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data?.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  const handleResentOtp = async () => {
    try {
      setLoading(true);
      setSuccess(false);
      setFailed(false);

      const msisdn = "263" + parseInt(phoneNumber);
      const response = await AuthService.sendOtpForLogin(msisdn);

      if (response.data && response.data.success) {
        setLoading(false);
        setSuccess(true);
        setSuccessMessage("OTP sent successfully");
      } else {
        setFailed(true);
        setLoading(false);
        setErrorMessage("Failed to send otp, Please try again");
      }
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status === 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status === 403 ||
          error.response.status === 500 ||
          error.response.status === 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data?.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  return (
    <View
      w="100%"
      bg={"white"}
      h={"100%"}
      px={moderateScale(13)}
      pt={moderateScale(4)}>
      <LoadingModal isLoading={loading} />
      <Toast
        visible={failed}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"red"}
        opacity={0.9}>
        {errorMessage}
      </Toast>
      <Toast
        visible={success}
        position={30}
        shadow={true}
        animation={true}
        hideOnPress={true}
        backgroundColor={"green"}
        opacity={0.9}>
        {successMessage}
      </Toast>
      <Box
        safeArea
        px={moderateScale(10)}
        pt={moderateScale(24)}
        py={moderateScale(8)}
        w="100%">
        <Heading
          fontSize={moderateScale(20)}
          fontFamily={"openSansSemiBold"}
          fontWeight="600"
          color="coolGray.800">
          Enter Verification Code
        </Heading>
        <Heading
          mt={moderateScale(20)}
          fontFamily={"openSansMedium"}
          color="#F68C1E"
          fontWeight="openSansMedium"
          fontSize={moderateScale(15)}>
          Enter the 4-digit code we just texted to your phone number,{" "}
          {phoneNumber}
        </Heading>

        <TouchableOpacity
          onPress={() => {
            navigation.goBack();
          }}>
          <Heading
            mt={moderateScale(8)}
            fontFamily={"openSans"}
            underline
            color="gray.600"
            fontWeight="openSansMedium"
            fontSize={moderateScale(15)}>
            Edit Number
          </Heading>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            handleResentOtp();
          }}>
          <Heading
            fontFamily={"openSans"}
            mt={moderateScale(8)}
            underline
            color="gray.600"
            fontWeight="openSansMedium"
            fontSize={moderateScale(15)}>
            Resend Code
          </Heading>
        </TouchableOpacity>

        <VStack space={3} mt="50">
          <FormControl>
            <Pressable onPress={Keyboard.dismiss}>
              <OTPInput
                code={otpCode}
                setCode={setOTPCode}
                maximumLength={maximumCodeLength}
                setIsPinReady={setIsPinReady}
              />
            </Pressable>
          </FormControl>

          {!isPinReady ? (
            <></>
          ) : (
            <Button
              mt="10"
              onPress={() => {
                handleVerify();
              }}
              h={verticalScale(48)}
              bg="#F68C1E"
              rounded={"lg"}>
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Continue
              </Text>
            </Button>
          )}
        </VStack>
      </Box>
    </View>
  );
};

export default LoginOTP;
