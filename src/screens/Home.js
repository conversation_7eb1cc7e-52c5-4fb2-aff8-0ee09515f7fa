import { <PERSON>, HStack, <PERSON><PERSON>View, Text, VStack, StatusBar } from "native-base";
import React from "react";
import { SafeAreaView, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";
import { FontAwesome5, MaterialCommunityIcons } from "@expo/vector-icons";

const Home = () => {
  const services = [
    {
      icon: "sim-card",
      iconType: "FontAwesome5",
      name: "Reset Puk",
      bgColor: "#FF8500",
    },
    {
      icon: "link",
      iconType: "FontAwesome5",
      name: "Link Numbers",
      bgColor: "#10B981",
    },
    {
      icon: "lock",
      iconType: "FontAwesome5",
      name: "Change Password",
      bgColor: "#3B82F6",
    },
    {
      icon: "form-textbox-password",
      iconType: "MaterialCommunityIcons",
      name: "Change Passphrase",
      bgColor: "#8B5CF6",
    },
    {
      icon: "user-alt",
      iconType: "FontAwesome5",
      name: "My Profile",
      bgColor: "#EF4444",
    },
    {
      icon: "dialpad",
      iconType: "MaterialCommunityIcons",
      name: "USSD Codes",
      bgColor: "#F59E0B",
    },
  ];

  const renderIcon = (item) => {
    if (item.iconType === "FontAwesome5") {
      return (
        <FontAwesome5 name={item.icon} size={moderateScale(24)} color="white" />
      );
    } else {
      return (
        <MaterialCommunityIcons
          name={item.icon}
          size={moderateScale(24)}
          color="white"
        />
      );
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <Text
              fontFamily={"openSansBold"}
              fontSize={moderateScale(28)}
              color={"gray.900"}>
              Services
            </Text>
            <Text
              fontFamily={"openSansMedium"}
              fontSize={moderateScale(16)}
              color={"gray.500"}>
              Manage your account and settings
            </Text>
          </VStack>
        </Box>

        {/* Services Grid */}
        <Box px={moderateScale(20)} py={moderateScale(20)}>
          <VStack space={moderateScale(16)}>
            {/* First Row */}
            <HStack space={moderateScale(12)}>
              {services.slice(0, 2).map((item, index) => (
                <TouchableOpacity key={index} style={{ flex: 1 }}>
                  <Box
                    bg={item.bgColor}
                    rounded={"2xl"}
                    p={moderateScale(20)}
                    h={moderateScale(120)}
                    justifyContent={"space-between"}>
                    <Box
                      bg={"rgba(255,255,255,0.2)"}
                      rounded={"full"}
                      w={moderateScale(48)}
                      h={moderateScale(48)}
                      alignItems={"center"}
                      justifyContent={"center"}>
                      {renderIcon(item)}
                    </Box>
                    <Text
                      fontFamily={"openSansBold"}
                      color={"white"}
                      fontSize={moderateScale(16)}>
                      {item.name}
                    </Text>
                  </Box>
                </TouchableOpacity>
              ))}
            </HStack>

            {/* Second Row */}
            <HStack space={moderateScale(12)}>
              {services.slice(2, 4).map((item, index) => (
                <TouchableOpacity key={index} style={{ flex: 1 }}>
                  <Box
                    bg={item.bgColor}
                    rounded={"2xl"}
                    p={moderateScale(20)}
                    h={moderateScale(120)}
                    justifyContent={"space-between"}>
                    <Box
                      bg={"rgba(255,255,255,0.2)"}
                      rounded={"full"}
                      w={moderateScale(48)}
                      h={moderateScale(48)}
                      alignItems={"center"}
                      justifyContent={"center"}>
                      {renderIcon(item)}
                    </Box>
                    <Text
                      fontFamily={"openSansBold"}
                      color={"white"}
                      fontSize={moderateScale(16)}>
                      {item.name}
                    </Text>
                  </Box>
                </TouchableOpacity>
              ))}
            </HStack>

            {/* Third Row */}
            <HStack space={moderateScale(12)}>
              {services.slice(4, 6).map((item, index) => (
                <TouchableOpacity key={index} style={{ flex: 1 }}>
                  <Box
                    bg={item.bgColor}
                    rounded={"2xl"}
                    p={moderateScale(20)}
                    h={moderateScale(120)}
                    justifyContent={"space-between"}>
                    <Box
                      bg={"rgba(255,255,255,0.2)"}
                      rounded={"full"}
                      w={moderateScale(48)}
                      h={moderateScale(48)}
                      alignItems={"center"}
                      justifyContent={"center"}>
                      {renderIcon(item)}
                    </Box>
                    <Text
                      fontFamily={"openSansBold"}
                      color={"white"}
                      fontSize={moderateScale(16)}>
                      {item.name}
                    </Text>
                  </Box>
                </TouchableOpacity>
              ))}
            </HStack>
          </VStack>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Home;
