import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { Box, HStack, ScrollView, StatusBar, Text, VStack } from "native-base";
import React, { useEffect, useState } from "react";
import { SafeAreaView, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";
import LocalStore from "../../utilities/store";
import LoadingModal from "../../components/Loading/LoadingModal";
import { useAuth } from "../../utilities/AuthContext";

const Account = ({ route }) => {
  const navigation = useNavigation();
  const { logout: authLogout } = useAuth();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const getProfile = async () => {
      const profile = await LocalStore.getData("@userProfile");
      if (profile !== null) {
        setProfile(profile);
      }
    };
    getProfile();
  }, []);

  const handleLogout = async () => {
    setLoading(true);
    const success = await authLogout();
    if (success) {
      navigation.navigate("Landing");
    }
    setLoading(false);
  };

  const accountItems = [
    {
      key: 4,
      name: "Netone Shops",
      route: "shops",
      ref: "SHOP",
      icon: "shopping",
      bgColor: "#F68C1E",
      description: "Find nearby stores",
    },
    {
      key: 5,
      name: "Contact Us",
      route: "contact",
      ref: "CONTACT",
      icon: "phone",
      bgColor: "#F68C1E",
      description: "Get in touch with us",
    },
  ];

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        <LoadingModal isLoading={loading} />

        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <Text
              fontFamily={"openSansBold"}
              fontSize={moderateScale(28)}
              color={"gray.900"}>
              Account
            </Text>
            <Text
              fontFamily={"openSansMedium"}
              fontSize={moderateScale(16)}
              color={"gray.500"}>
              Manage your account and preferences
            </Text>
          </VStack>
        </Box>

        {/* Account Items */}
        <Box px={moderateScale(20)} py={moderateScale(20)}>
          <VStack space={moderateScale(16)}>
            {/* Account Services */}
            <VStack space={moderateScale(12)}>
              {accountItems.map((item) => (
                <TouchableOpacity
                  key={item.key}
                  onPress={() => {
                    navigation.navigate(item.route, {
                      options: [],
                      title: item.name,
                      ref: item.ref,
                      phone: profile?.phoneNumber,
                    });
                  }}>
                  <Box
                    bg={"white"}
                    rounded={"2xl"}
                    p={moderateScale(20)}
                    shadow={2}
                    borderWidth={1}
                    borderColor={"gray.100"}>
                    <HStack space={moderateScale(16)} alignItems={"center"}>
                      <Box
                        bg={item.bgColor}
                        rounded={"full"}
                        w={moderateScale(48)}
                        h={moderateScale(48)}
                        alignItems={"center"}
                        justifyContent={"center"}>
                        <MaterialCommunityIcons
                          name={item.icon}
                          size={moderateScale(24)}
                          color="white"
                        />
                      </Box>
                      <VStack flex={1} space={moderateScale(4)}>
                        <Text
                          fontFamily={"openSansBold"}
                          fontSize={moderateScale(16)}
                          color={"gray.900"}>
                          {item.name}
                        </Text>
                        <Text
                          fontFamily={"openSansMedium"}
                          fontSize={moderateScale(14)}
                          color={"gray.500"}>
                          {item.description}
                        </Text>
                      </VStack>
                      <MaterialCommunityIcons
                        name="chevron-right"
                        size={moderateScale(20)}
                        color="#9CA3AF"
                      />
                    </HStack>
                  </Box>
                </TouchableOpacity>
              ))}
            </VStack>

            {/* Logout Button */}
            <TouchableOpacity onPress={handleLogout}>
              <Box
                bg={"#EF4444"}
                rounded={"2xl"}
                p={moderateScale(20)}
                shadow={2}>
                <HStack
                  space={moderateScale(16)}
                  alignItems={"center"}
                  justifyContent={"center"}>
                  <MaterialCommunityIcons
                    name="logout"
                    size={moderateScale(24)}
                    color="white"
                  />
                  <Text
                    fontFamily={"openSansBold"}
                    fontSize={moderateScale(16)}
                    color={"white"}>
                    Logout
                  </Text>
                </HStack>
              </Box>
            </TouchableOpacity>
          </VStack>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default Account;
