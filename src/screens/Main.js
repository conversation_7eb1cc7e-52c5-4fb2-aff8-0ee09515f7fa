import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  AlertDialog,
  Box,
  Button,
  HStack,
  ScrollView,
  StatusBar,
  Text,
  VStack,
} from "native-base";
import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import {
  Linking,
  SafeAreaView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  Image,
  Platform,
} from "react-native";
import { moderateScale } from "react-native-size-matters";
import LoadingModal from "../components/Loading/LoadingModal";
import LocalStore from "../utilities/store";
import Balance from "../services/Balance/Balance";
import ActionSheet from "react-native-actions-sheet";
import { useAuth } from "../utilities/AuthContext";
import logo from "../assets/Icons/netone2.png";

const Main4 = () => {
  const navigation = useNavigation();
  const { logout: authLogout, userInfo } = useAuth();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [Phone, setPhone] = useState(null);
  const [init, setInit] = useState("");
  const [failed, setFailed] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const cancelRef = useRef(null);
  const onClose = () => setIsOpen(false);
  const [balance, setBalance] = useState([]);
  const actionSheetRef = useRef(null);
  const [loadingSend, setLoadingSend] = useState(false);
  const [loadingBalance, setLoadingBalance] = useState(false);
  const [usd, setUsd] = useState(0.0);
  const [ZWG, setZWG] = useState(0.0);
  const [isPostpaid, setIsPostpaid] = useState(false);
  const [postpaidData, setPostpaidData] = useState(null);
  const [subscriberProfile, setSubscriberProfile] = useState(null);

  const { width } = Dimensions.get("window");

  const logout = async () => {
    setLoading(true);
    const success = await authLogout();
    if (success) {
      navigation.navigate("Landing");
    }
    setLoading(false);
  };

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);

    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const getProfile = async () => {
      const Phone = await LocalStore.getData("@username");
      //check if phone number is stored
      if (Phone !== null) {
        setPhone(Phone);
        checkSubscriberType(Phone);
      }
    };
    getProfile();
  }, []);

  const checkSubscriberType = async (phone) => {
    try {
      const response = await Balance.getSubscriberProfile("263" + phone);

      if (response.data.success) {
        const profile = response.data.body;
        setSubscriberProfile(profile);
        const isPostpaidUser = profile.postPaid === "1";
        setIsPostpaid(isPostpaidUser);

        // Load balance based on subscriber type
        if (isPostpaidUser) {
          handlePostpaidBalanceEnquiry(phone, true);
        } else {
          handleBalanceEnquiry(false, phone, true);
        }
      }
    } catch (error) {
      console.log("Error checking subscriber type:", error);
      // Fallback to prepaid if error
      setIsPostpaid(false);
      handleBalanceEnquiry(false, phone, true);
    }
  };

  const handlePostpaidBalanceEnquiry = async (phone, noloading = false) => {
    try {
      !noloading && setLoading(true);
      setFailed(false);

      const response = await Balance.getPostpaidBalance("263" + phone);
      console.log(response);

      if (response.data.success) {
        setPostpaidData(response.data.body);
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      handleError(error);
    }
  };

  const handleError = (error) => {
    if (error.response) {
      if (error.response.status === 400) {
        setErrorMessage("Something went wrong, Please try again");
      } else if (
        error.response.status === 403 ||
        error.response.status === 500 ||
        error.response.status === 504
      ) {
        setErrorMessage("Server Error, Please try again later");
      } else {
        if (error.response.data.message) {
          setErrorMessage(error.response.data.message);
        } else {
          setErrorMessage("Something went wrong, Please try again");
        }
      }
    } else if (error.request) {
      setErrorMessage("Network Error, Please check your internet connection");
    } else {
      setErrorMessage("Something went wrong, Please try again");
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    if (isPostpaid) {
      await handlePostpaidBalanceEnquiry(Phone);
    } else {
      await handleBalanceEnquiry(false, Phone);
    }
    setRefreshing(false);
  };

  useLayoutEffect(() => {
    (async function getBalance() {
      const ZWG = await LocalStore.getData("@ZWG");
      const usd = await LocalStore.getData("@usd");

      if (ZWG !== null) {
        setZWG(ZWG);
      }

      if (usd !== null) {
        setUsd(usd);
      }
    })();
  }, []);

  const handleBalanceEnquiry = async (viewAll, Phone, noloading) => {
    try {
      noloading ? "" : setLoading(true);
      setFailed(false);

      const response = await Balance.getBalance("263" + Phone);
      console.log(response.data);

      if (response.data.success) {
        setLoading(false);

        if (viewAll) {
          const filteredData = [];
          const remainingData = [];

          //combine bundles
          const combinedData = Object.values(
            response.data.body.reduce((acc, item) => {
              if (!acc[item.acctResId]) {
                acc[item.acctResId] = { ...item };
              } else {
                acc[item.acctResId].balance += item.balance;
              }
              return acc;
            }, {})
          );

          combinedData.forEach((item) => {
            if (item.accountResName.includes("Data")) {
              filteredData.push({
                ...item,
                balance: `${Math.abs(item.balance / (1024 * 1024)).toFixed(
                  2
                )} MB`,
              });
            } else if (item.accountResName.includes("Voice")) {
              filteredData.push({
                ...item,
                balance: `${Math.abs(item.balance / 60)} minutes`,
              });
            } else {
              remainingData.push({
                ...item,
                balance: Math.abs(item.balance.toFixed(3)),
              });
            }
          });

          const usd = remainingData.find(
            (item) => item.accountResName === "USD Currency Balance"
          );
          const ZWG = remainingData.find(
            (item) => item.accountResName === "ZWG Currency"
          );
          const others = remainingData.filter(
            (item) =>
              item.accountResName !== "USD Currency Balance" &&
              item.accountResName !== "ZWG Currency"
          );

          setBalance([usd, ZWG, ...filteredData, ...others]);

          setTimeout(() => {
            actionSheetRef.current.show();
          }, 500);
        } else {
          setUsd(0.0);
          LocalStore.saveData("@usd", 0.0);
          response.data.body.forEach((element) => {
            if (element.accountResName === "ZWG Currency") {
              setZWG(element.balance);
              LocalStore.saveData("@ZWG", element.balance);
            }

            if (element.accountResName === "USD Currency Balance") {
              setUsd(element.balance);
              LocalStore.saveData("@usd", element.balance);
            }
          });
        }
      } else {
        setFailed(true);
        setErrorMessage(response.data.message);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setFailed(true);
      console.log(error);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  };

  // Main Balance Card Component
  const BalanceCard = () => {
    if (isPostpaid && postpaidData) {
      return (
        <Box
          bg={"white"}
          rounded={"2xl"}
          p={moderateScale(20)}
          shadow={2}
          w={"full"}>
          <VStack space={moderateScale(12)}>
            <HStack justifyContent={"space-between"} alignItems={"center"}>
              <Text
                fontFamily={"openSansMedium"}
                color={"gray.500"}
                fontSize={moderateScale(14)}>
                Credit Limit Remaining
              </Text>
              <TouchableOpacity
                onPress={() =>
                  postpaidData && handleBalanceEnquiry(true, Phone)
                }>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={moderateScale(20)}
                  color="#9CA3AF"
                />
              </TouchableOpacity>
            </HStack>

            <Text
              fontFamily={"openSansBold"}
              color={"gray.900"}
              fontSize={moderateScale(36)}>
              $
              {postpaidData
                ? Math.abs(postpaidData.creditLimitRemaining).toFixed(2)
                : "0.00"}
            </Text>

            {/* Additional Info for Postpaid */}
            <HStack justifyContent={"space-between"} mt={moderateScale(12)}>
              <VStack alignItems={"flex-start"}>
                <Text
                  fontFamily={"openSansMedium"}
                  color={"gray.500"}
                  fontSize={moderateScale(12)}>
                  Credit Limit
                </Text>
                <Text
                  fontFamily={"openSansBold"}
                  color={"gray.700"}
                  fontSize={moderateScale(16)}>
                  ${postpaidData ? postpaidData.creditLimit.toFixed(2) : "0.00"}
                </Text>
              </VStack>
              <VStack alignItems={"flex-end"}>
                <Text
                  fontFamily={"openSansMedium"}
                  color={"gray.500"}
                  fontSize={moderateScale(12)}>
                  Credits Used
                </Text>
                <Text
                  fontFamily={"openSansBold"}
                  color={"gray.700"}
                  fontSize={moderateScale(16)}>
                  ${postpaidData ? postpaidData.creditsUsed.toFixed(2) : "0.00"}
                </Text>
              </VStack>
            </HStack>

            <TouchableOpacity
              onPress={() => handlePostpaidBalanceEnquiry(Phone)}
              style={{ marginTop: moderateScale(16) }}>
              <Box
                bg={"gray.100"}
                rounded={"xl"}
                py={moderateScale(12)}
                alignItems={"center"}>
                <Text
                  fontFamily={"openSansSemiBold"}
                  color={"gray.700"}
                  fontSize={moderateScale(14)}>
                  Refresh Balance
                </Text>
              </Box>
            </TouchableOpacity>
          </VStack>
        </Box>
      );
    }

    // Prepaid Balance Card
    return (
      <Box
        bg={"white"}
        rounded={"2xl"}
        p={moderateScale(20)}
        shadow={2}
        w={"full"}>
        <VStack space={moderateScale(12)}>
          <HStack justifyContent={"space-between"} alignItems={"center"}>
            <Text
              fontFamily={"openSansMedium"}
              color={"gray.500"}
              fontSize={moderateScale(14)}>
              SIM Balance
            </Text>
            <TouchableOpacity onPress={() => handleBalanceEnquiry(true, Phone)}>
              <MaterialCommunityIcons
                name="chevron-right"
                size={moderateScale(20)}
                color="#9CA3AF"
              />
            </TouchableOpacity>
          </HStack>

          {/* Balance Display */}
          <HStack space={moderateScale(20)} justifyContent={"center"}>
            <VStack alignItems={"center"}>
              <Text
                fontFamily={"openSansMedium"}
                color={"gray.500"}
                fontSize={moderateScale(12)}>
                ZWG
              </Text>
              <Text
                fontFamily={"openSansBold"}
                color={"gray.900"}
                fontSize={moderateScale(24)}>
                {Math.abs(ZWG).toFixed(2)}
              </Text>
            </VStack>
            <VStack alignItems={"center"}>
              <Text
                fontFamily={"openSansMedium"}
                color={"gray.500"}
                fontSize={moderateScale(12)}>
                USD
              </Text>
              <Text
                fontFamily={"openSansBold"}
                color={"gray.900"}
                fontSize={moderateScale(24)}>
                ${Math.abs(usd).toFixed(2)}
              </Text>
            </VStack>
          </HStack>

          {/* Add Credit button only for prepaid */}
          <TouchableOpacity
            onPress={() =>
              navigation.navigate("airtimePurchase", { phone: Phone })
            }
            style={{ marginTop: moderateScale(16) }}>
            <Box
              bg={"gray.100"}
              rounded={"xl"}
              py={moderateScale(12)}
              alignItems={"center"}>
              <Text
                fontFamily={"openSansSemiBold"}
                color={"gray.700"}
                fontSize={moderateScale(14)}>
                Add Credit
              </Text>
            </Box>
          </TouchableOpacity>
        </VStack>
      </Box>
    );
  };

  // Service Cards Component
  const ServiceCards = () => {
    const primaryServices = [
      {
        key: "bundles",
        icon: "wifi",
        name: "airtimeBundle",
        displayName: "Buy bundles",
        subtitle: "Data & Voice bundles",
        bgColor: "#FF8500",
        iconBg: "#D97706",
      },
      {
        key: "onemoney",
        icon: "credit-card-chip",
        name: "airtimeBundle",
        displayName: "One Money",
        subtitle: "Mobile wallet",
        link: true,
        url:
          Platform.OS === "ios"
            ? "https://apps.apple.com/zw/app/onemoney-mobile/id6737691641"
            : "https://play.google.com/store/apps/details?id=zw.co.onemoney.mob",
        bgColor: "#FF8500",
        iconBg: "#D97706",
      },
      // Conditional service based on subscriber type
      isPostpaid
        ? {
            key: "shops",
            icon: "shopping",
            name: "shops",
            displayName: "Netone Shops",
            subtitle: "Find nearby stores",
            bgColor: "#FF8500",
            iconBg: "#D97706",
          }
        : {
            key: "creditTransfer",
            icon: "bank-transfer",
            name: "creditTransfer",
            displayName: "Credit Transfer",
            subtitle: "Transfer airtime",
            bgColor: "#FF8500",
            iconBg: "#D97706",
          },
      {
        key: "ussd",
        icon: "dialpad",
        name: "ussdCodes",
        displayName: "USSD Codes",
        subtitle: "Quick access codes",
        bgColor: "#FF8500",
        iconBg: "#D97706",
      },
    ];

    return (
      <VStack space={moderateScale(16)}>
        {/* Service Cards Grid */}
        <VStack space={moderateScale(12)}>
          {/* First Row */}
          <HStack space={moderateScale(12)}>
            {primaryServices.slice(0, 2).map((item) => (
              <TouchableOpacity
                key={item.key}
                style={{ flex: 1 }}
                onPress={async () => {
                  if (item.link) {
                    await Linking.openURL(item.url);
                  } else {
                    navigation.navigate(item.name, { phone: Phone });
                  }
                }}>
                <Box
                  bg={item.bgColor}
                  rounded={"2xl"}
                  p={moderateScale(16)}
                  h={moderateScale(140)}
                  justifyContent={"space-between"}>
                  <VStack space={moderateScale(8)}>
                    <Box
                      bg={item.iconBg}
                      rounded={"full"}
                      w={moderateScale(40)}
                      h={moderateScale(40)}
                      alignItems={"center"}
                      justifyContent={"center"}>
                      <MaterialCommunityIcons
                        name={item.icon}
                        size={moderateScale(20)}
                        color="white"
                      />
                    </Box>
                    <VStack>
                      <Text
                        fontFamily={"openSansBold"}
                        color={"white"}
                        fontSize={moderateScale(16)}>
                        {item.displayName}
                      </Text>
                      <Text
                        fontFamily={"openSansMedium"}
                        color={"white"}
                        opacity={0.8}
                        fontSize={moderateScale(12)}>
                        {item.subtitle}
                      </Text>
                    </VStack>
                  </VStack>
                </Box>
              </TouchableOpacity>
            ))}
          </HStack>

          {/* Second Row */}
          <HStack space={moderateScale(12)}>
            {primaryServices.slice(2, 4).map((item) => (
              <TouchableOpacity
                key={item.key}
                style={{ flex: 1 }}
                onPress={async () => {
                  if (item.link) {
                    await Linking.openURL(item.url);
                  } else {
                    navigation.navigate(item.name, { phone: Phone });
                  }
                }}>
                <Box
                  bg={item.bgColor}
                  rounded={"2xl"}
                  p={moderateScale(16)}
                  h={moderateScale(140)}
                  justifyContent={"space-between"}>
                  <VStack space={moderateScale(8)}>
                    <Box
                      bg={item.iconBg}
                      rounded={"full"}
                      w={moderateScale(40)}
                      h={moderateScale(40)}
                      alignItems={"center"}
                      justifyContent={"center"}>
                      <MaterialCommunityIcons
                        name={item.icon}
                        size={moderateScale(20)}
                        color="white"
                      />
                    </Box>
                    <VStack>
                      <Text
                        fontFamily={"openSansBold"}
                        color={"white"}
                        fontSize={moderateScale(16)}>
                        {item.displayName}
                      </Text>
                      <Text
                        fontFamily={"openSansMedium"}
                        color={"white"}
                        opacity={0.8}
                        fontSize={moderateScale(12)}>
                        {item.subtitle}
                      </Text>
                    </VStack>
                  </VStack>
                </Box>
              </TouchableOpacity>
            ))}
          </HStack>
        </VStack>
      </VStack>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView
        flex={1}
        bg={"gray.50"}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }>
        <LoadingModal isLoading={loading} />

        {/* Header Section */}
        <Box
          bg={"white"}
          w={"full"}
          px={moderateScale(20)}
          pt={moderateScale(0)}>
          <HStack
            justifyContent={"space-between"}
            alignItems={"center"}
            pb={moderateScale(20)}>
            <VStack>
              <Box
                w={moderateScale(200)}
                h={moderateScale(60)}
                alignItems={"flex-start"}>
                <Image
                  source={logo}
                  style={{
                    width: moderateScale(70),
                    height: moderateScale(70),
                    resizeMode: "contain",
                  }}
                />
              </Box>
            </VStack>
            <TouchableOpacity onPress={logout}>
              <Box
                bg={"gray.200"}
                rounded={"full"}
                w={moderateScale(40)}
                h={moderateScale(40)}
                alignItems={"center"}
                justifyContent={"center"}>
                <Text
                  fontFamily={"openSansBold"}
                  fontSize={moderateScale(16)}
                  color={"gray.600"}>
                  {userInfo?.firstName?.charAt(0) || "U"}
                </Text>
              </Box>
            </TouchableOpacity>
          </HStack>
        </Box>

        {/* Balance Card */}
        <Box px={moderateScale(20)} py={moderateScale(15)}>
          <BalanceCard />
        </Box>

        {/* Services Section */}
        <Box
          bg={"gray.50"}
          flex={1}
          px={moderateScale(20)}
          py={moderateScale(10)}>
          <ServiceCards />
        </Box>

        {/* Action Sheet for Balance Details */}
        <ActionSheet ref={actionSheetRef} gestureEnabled={true}>
          <Box h={moderateScale(400)} p={moderateScale(20)}>
            <Text
              fontSize={moderateScale(18)}
              fontFamily={"openSansBold"}
              mb={moderateScale(16)}
              textAlign={"center"}>
              Balance Details
            </Text>
            <ScrollView showsVerticalScrollIndicator={false}>
              <VStack space={moderateScale(8)}>
                {balance.map((item, index) => (
                  <HStack
                    key={item?.acctResId || index}
                    justifyContent={"space-between"}
                    py={moderateScale(12)}
                    borderBottomWidth={1}
                    borderBottomColor={"gray.200"}>
                    <Text
                      fontSize={moderateScale(14)}
                      fontFamily={"openSansMedium"}
                      flex={1}
                      color={"gray.700"}>
                      {item?.accountResName || "Unknown"}
                    </Text>
                    <Text
                      fontSize={moderateScale(14)}
                      fontFamily={"openSansBold"}
                      color={"gray.900"}>
                      {item?.balance || "0"}
                    </Text>
                  </HStack>
                ))}
              </VStack>
            </ScrollView>
          </Box>
        </ActionSheet>

        {/* Session Timeout Modal */}
        {/*         <SessionTimeOut isOpen={isOpen} onClose={onClose} cancelRef={cancelRef} /> */}
      </ScrollView>

      {/* Error Alert */}
      {failed && (
        <AlertDialog
          leastDestructiveRef={cancelRef}
          isOpen={failed}
          onClose={() => setFailed(false)}>
          <AlertDialog.Content>
            <AlertDialog.CloseButton />
            <AlertDialog.Header>Error</AlertDialog.Header>
            <AlertDialog.Body>
              <Text>{errorMessage}</Text>
            </AlertDialog.Body>
            <AlertDialog.Footer>
              <Button.Group space={2}>
                <Button
                  variant="unstyled"
                  colorScheme="coolGray"
                  onPress={() => setFailed(false)}
                  ref={cancelRef}>
                  Cancel
                </Button>
                <Button
                  colorScheme="danger"
                  onPress={() => {
                    setFailed(false);
                    if (isPostpaid) {
                      handlePostpaidBalanceEnquiry(Phone);
                    } else {
                      handleBalanceEnquiry(false, Phone);
                    }
                  }}>
                  Retry
                </Button>
              </Button.Group>
            </AlertDialog.Footer>
          </AlertDialog.Content>
        </AlertDialog>
      )}
    </SafeAreaView>
  );
};

export default Main4;
