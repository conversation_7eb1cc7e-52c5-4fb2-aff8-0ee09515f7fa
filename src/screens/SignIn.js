import { Feather, Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Actionsheet,
  Box,
  Button,
  Center,
  FormControl,
  HStack,
  Heading,
  Image,
  Input,
  InputGroup,
  InputLeftAddon,
  InputRightAddon,
  Link,
  Pressable,
  ScrollView,
  Select,
  Text,
  VStack,
  View,
} from "native-base";
import React, { useEffect, useState } from "react";
import { Keyboard, TouchableOpacity } from "react-native";
import Toast from "react-native-root-toast";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import netone from "../assets/Icons/netone2.png";
import LoadingModal from "../components/Loading/LoadingModal";
import Auth from "../services/Auth/Auth";
import LocalStore from "../utilities/store";
import { getUniqueId } from "react-native-device-info";
import UserAgent from "react-native-user-agent";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { StatusBar } from "expo-status-bar";
import { useAuth } from "../utilities/AuthContext";

const SignIn = () => {
  const navigation = useNavigation();
  const { login } = useAuth();
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    const interval = setTimeout(() => {
      setFailed(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [failed]);

  useEffect(() => {
    const interval = setTimeout(() => {
      setSuccess(false);
    }, 5000);
    return () => clearInterval(interval);
  }, [success]);

  const validate = () => {
    const numberRegexp = /^(071|71)/;

    if (!phone) {
      setPhoneError("This field is required");
      return false;
    }

    if (phone.length < 9) {
      setPhoneError("Invalid phone number, not enough digits");
      return false;
    }

    if (phone.length > 10) {
      setPhoneError("Invalid phone number, too many digits");
      return false;
    }

    if (!numberRegexp.test(phone)) {
      setPhoneError("Invalid phone number, use a netone number");
      return false;
    }

    return true;
  };

  const handleSendOtp = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }

      setLoading(true);
      setFailed(false);

      const msisdn = "263" + parseInt(phone);
      const response = await Auth.sendOtpForLogin(msisdn);

      if (response.data && response.data.success) {
        setLoading(false);
        // Navigate to separate OTP screen
        navigation.navigate("LoginOtp", {
          number: phone,
          to: "signIn",
        });
      } else {
        setFailed(true);
        setErrorMessage(
          response.data?.message || "Failed to send OTP, please try again"
        );
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      setFailed(true);
      handleError(error);
    }
  };

  const handleError = (error) => {
    if (error.response) {
      if (error.response.status === 400) {
        setErrorMessage("Something went wrong, Please try again");
      } else if (error.response.status === 401) {
        setErrorMessage("Invalid credentials");
      } else if (
        error.response.status === 403 ||
        error.response.status === 500 ||
        error.response.status === 504
      ) {
        setErrorMessage("Server Error, Please try again later");
      } else {
        if (error.response.data?.message) {
          setErrorMessage(error.response.data.message);
        } else {
          setErrorMessage("Something went wrong, Please try again");
        }
      }
    } else if (error.request) {
      setErrorMessage("Network Error, Please check your internet connection");
    } else {
      setErrorMessage("Something went wrong, Please try again");
    }
  };

  return (
    <View
      w="100%"
      bg={"white"}
      h={"100%"}
      px={moderateScale(24)}
      pt={moderateScale(0)}>
      <StatusBar style="dark" backgroundColor="#E5E6E8" />
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        extraScrollHeight={100}>
        <LoadingModal isLoading={loading} />
        <Box safeArea w="100%">
          <Toast
            visible={failed}
            position={30}
            shadow={true}
            animation={true}
            hideOnPress={true}
            backgroundColor={"red"}
            opacity={0.9}>
            {errorMessage}
          </Toast>
          <Toast
            visible={success}
            position={30}
            shadow={true}
            animation={true}
            hideOnPress={true}
            backgroundColor={"green"}
            opacity={0.9}>
            {successMessage}
          </Toast>

          <Center>
            <View p={moderateScale(4)}>
              <Image
                source={netone}
                height={verticalScale(100)}
                width={scale(150)}
                alt={"OneMoney"}
              />
            </View>
            <Heading
              fontSize={moderateScale(20)}
              mt={moderateScale(30)}
              fontFamily={"openSansSemiBold"}
              fontWeight="600"
              color="coolGray.800">
              Welcome!
            </Heading>
          </Center>

          <VStack space={moderateScale(16)} mt={moderateScale(48)}>
            <FormControl>
              <InputGroup>
                <InputLeftAddon
                  isDisabled={phone ? true : false}
                  roundedLeft={"lg"}
                  w={scale(60)}
                  children={
                    <Text
                      fontSize={moderateScale(15)}
                      fontFamily={"openSansMedium"}
                      color={"gray.600"}>
                      {"+263"}
                    </Text>
                  }
                />
                <Input
                  flex={1}
                  defaultValue={`${phone}`}
                  fontFamily={"openSansSemiBold"}
                  onFocus={() => {
                    setPhoneError("");
                  }}
                  focusOutlineColor={"brand.500"}
                  borderColor={phoneError ? "red.400" : "gray.300"}
                  placeholder={"Netone Phone Number"}
                  fontSize={moderateScale(15)}
                  bgColor={"gray.100"}
                  rounded={"lg"}
                  onChangeText={(text) => {
                    setPhone(text);
                  }}
                  h={verticalScale(48)}
                  keyboardType={"number-pad"}
                />
              </InputGroup>
              <Text color={"red.400"} fontSize={moderateScale(15)}>
                {phoneError}
              </Text>
            </FormControl>

            <Button
              mt={moderateScale(24)}
              onPress={handleSendOtp}
              h={verticalScale(48)}
              bg="brand.500"
              rounded={"lg"}>
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Sign In
              </Text>
            </Button>
          </VStack>
        </Box>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default SignIn;
