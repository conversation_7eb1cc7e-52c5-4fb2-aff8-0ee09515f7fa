import { FontAwesome } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import LottieView from "lottie-react-native";
import { Button, Center, HStack, Text, VStack } from "native-base";
import React, { useEffect, useRef, useState } from "react";
import { StyleSheet, useWindowDimensions } from "react-native";
import { moderateScale } from "react-native-size-matters";

const SuccessSendPage = ({ route }) => {
  const animation = useRef(null);
  const navigation = useNavigation();
  const { width, height } = useWindowDimensions();

  useEffect(() => {
    // You can control the ref programmatically, rather than using autoPlay
    animation.current?.play();
  }, []);

  return (
    <VStack
      height={height}
      flex={1}
      bg={"white"}
      px={moderateScale(40)}
      py={moderateScale(10)}>
      <Center flex={2}>
        <LottieView
          autoPlay
          ref={animation}
          style={{
            width: 200,
            height: 200,
            backgroundColor: "white",
            top: 40,
            right: 25,
          }}
          // Find more Lottie files at https://lottiefiles.com/featured
          source={require("../../assets/lottie/success.json")}
        />
        <Text
          style={styles.text}
          fontSize={moderateScale(20)}
          fontFamily={"openSansBold"}>
          {route.params.data.message}
        </Text>

        <VStack mt={moderateScale(200)} space={moderateScale(10)}>
          <Button
            onPressIn={() => {
              navigation.navigate("Tabs");
            }}
            w={moderateScale(250)}
            bg="brand.500"
            rounded={"lg"}>
            <HStack space={4} py={moderateScale(4)} px={moderateScale(4)}>
              <FontAwesome
                name="reply"
                size={moderateScale(20)}
                color="white"
              />
              <Text
                fontSize={moderateScale(15)}
                color="white"
                fontFamily={"openSansSemiBold"}>
                Return To Home
              </Text>
            </HStack>
          </Button>
        </VStack>
      </Center>
    </VStack>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#fff",
  },
  image: {
    width: 200,
    height: 200,
    marginBottom: 30,
  },
  text: {
    marginBottom: 10,
  },
  subtext: {
    fontSize: 16,
    textAlign: "center",
    paddingHorizontal: 20,
  },
});

export default SuccessSendPage;
