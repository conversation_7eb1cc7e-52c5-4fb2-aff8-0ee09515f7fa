import { Octicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { BlurView } from "expo-blur";
import { Button, Center, HStack, Image, Text, VStack, View } from "native-base";
import React from "react";
import { ImageBackground, Platform, useWindowDimensions } from "react-native";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";
import LandingImage from "../assets/landing3.jpg";
import netone from "../assets/Icons/netone.png";
import { StatusBar } from "expo-status-bar";

const Landing = () => {
  const navigation = useNavigation();
  const { width, height } = useWindowDimensions();

  return (
    <>
      <StatusBar style="dark" backgroundColor="#FF8500" />
      <ImageBackground
        source={LandingImage}
        blurRadius={0}
        resizeMode={Platform.OS == "android" ? "cover" : "cover"}
        style={{
          width: "100%",
          height: "100%",
          justifyContent: "center",
        }}>
        <VStack height={height} bg={"transparent"} flex={1}>
          <View flex={3}></View>
          <View flex={1}>
            <View>
              <BlurView
                intensity={50}
                tint="dark"
                style={{
                  overflow: "hidden",
                }}>
                <View
                  h={"100%"}
                  justifyContent={"center"}
                  flexDirection={"column"}
                  px={moderateScale(16)}>
                  <View h={verticalScale(15)}></View>
                  <VStack h={verticalScale(40)} alignItems={"center"}>
                    <Text
                      fontWeight={"bold"}
                      fontFamily={"openSansBold"}
                      color={"white"}
                      fontSize={moderateScale(18)}>
                      NetOne, The World In One
                    </Text>
                  </VStack>
                  <View h={verticalScale(15)}></View>
                  <Button
                    bgColor={"white"}
                    my={moderateScale(4)}
                    rounded={"lg"}
                    h={verticalScale(48)}
                    onPress={() => {
                      navigation.navigate("SignIn");
                    }}>
                    <HStack space={4} alignItems={"center"}>
                      <Text
                        fontWeight={"bold"}
                        fontFamily={"openSansBold"}
                        color={"brand.500"}
                        fontSize={moderateScale(18)}>
                        Get started
                      </Text>
                      <Octicons name="arrow-right" size={24} color="#FF8500" />
                    </HStack>
                  </Button>
                </View>
              </BlurView>
              {/*               <Center>
                <View
                  position={"absolute"}
                  bg={"transparent"}
                  rounded={"2xl"}
                  padding={moderateScale(5)}
                  zIndex={"100"}
                  top={
                    Platform.OS == "android"
                      ? moderateScale(-260)
                      : moderateScale(-290)
                  }>
                  <Image
                    source={netone}
                    height={verticalScale(80)}
                    width={scale(100)}
                    alt={"OneMoney"}
                  />
                </View>
              </Center> */}
            </View>
          </View>
        </VStack>
      </ImageBackground>
    </>
  );
};

export default Landing;
