import {
  Box,
  HStack,
  Input,
  ScrollView,
  StatusBar,
  Text,
  VStack,
} from "native-base";
import React, { useState } from "react";
import { moderateScale } from "react-native-size-matters";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Linking, SafeAreaView, TouchableOpacity } from "react-native";

// import { Container } from './styles';

const UssdCodes = () => {
  const data = [
    {
      name: "OneMoney menu",
      code: "*111#",
      phone: "*111#",
    },
    {
      name: "NetOne self-care",
      code: "*123#",
      phone: "*123#",
    },
    {
      name: "Out of bundle browsing on or off",
      code: "*130#, option 2 (Block), restart device",
      phone: "*130#",
    },
    {
      name: "Own Number	",
      code: "*120# and select option 2",
      phone: "*120#",
    },
    {
      name: "Activate Incoming Barrings",
      code: "*35*0000#",
      phone: "*35*0000#",
    },
    {
      name: "Activate Outgoing Barrings",
      code: "*33*0000#",
      phone: "*33*0000#",
    },
    {
      name: "Airtime Balance",
      code: "*171#  option Balance Enquiry",
      phone: "*171#",
    },
    {
      name: "ZWL Bundle balance",
      code: "*171#, Balance Enquiry, Selet bundle in question",
      phone: "*171#",
    },
    {
      name: "USD Bundle Purchase ",
      code: "*379#",
      phone: "*379#",
    },
    {
      name: "USD Bundle Balance",
      code: "*379#",
      phone: "*379#",
    },
    {
      name: "Airtime Top Up",
      code: "*133*recharge pin#",
      phone: "*133#",
    },
    {
      name: "Voter registration check",
      code: "*265#",
      phone: "*265#",
    },
  ];

  const [searchTerm, setSearchTerm] = useState("");

  const filteredData = data.filter(
    (item) =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <Text
              fontFamily={"openSansMedium"}
              fontSize={moderateScale(16)}
              color={"gray.500"}>
              Quick access codes for NetOne services
            </Text>
          </VStack>
        </Box>

        {/* Search Bar */}
        <Box px={moderateScale(20)} py={moderateScale(16)}>
          <HStack
            bg={"white"}
            rounded={"xl"}
            p={moderateScale(12)}
            shadow={1}
            borderWidth={1}
            borderColor={"gray.100"}
            alignItems={"center"}
            space={moderateScale(12)}>
            <MaterialCommunityIcons
              name="magnify"
              size={moderateScale(20)}
              color="#9CA3AF"
            />
            <Input
              flex={1}
              fontFamily={"openSansMedium"}
              placeholder="Search USSD codes or services"
              fontSize={moderateScale(14)}
              borderWidth={0}
              bg={"transparent"}
              onChangeText={(text) => setSearchTerm(text)}
              value={searchTerm}
              _focus={{
                borderWidth: 0,
                bg: "transparent",
              }}
            />
          </HStack>
        </Box>

        {/* USSD Codes List */}
        <Box px={moderateScale(20)} pb={moderateScale(20)}>
          <VStack space={moderateScale(12)}>
            {filteredData.map((item, index) => (
              <Box
                key={index}
                bg={"white"}
                rounded={"2xl"}
                p={moderateScale(20)}
                shadow={2}
                borderWidth={1}
                borderColor={"gray.100"}>
                <HStack space={moderateScale(16)} alignItems={"center"}>
                  <Box
                    bg={"brand.500"}
                    rounded={"full"}
                    w={moderateScale(48)}
                    h={moderateScale(48)}
                    alignItems={"center"}
                    justifyContent={"center"}>
                    <MaterialCommunityIcons
                      name="dialpad"
                      size={moderateScale(24)}
                      color="white"
                    />
                  </Box>
                  <VStack flex={1} space={moderateScale(8)}>
                    <Text
                      fontFamily={"openSansBold"}
                      fontSize={moderateScale(16)}
                      color={"gray.900"}>
                      {item.name}
                    </Text>
                    <Text
                      fontFamily={"openSansMedium"}
                      fontSize={moderateScale(14)}
                      color={"gray.600"}
                      lineHeight={moderateScale(20)}>
                      {item.code}
                    </Text>
                  </VStack>
                  <TouchableOpacity
                    onPress={() => {
                      Linking.openURL(`tel:${item.phone}`);
                    }}>
                    <Box
                      bg={"gray.100"}
                      rounded={"full"}
                      w={moderateScale(40)}
                      h={moderateScale(40)}
                      alignItems={"center"}
                      justifyContent={"center"}>
                      <MaterialCommunityIcons
                        name="phone"
                        size={moderateScale(20)}
                        color="#F68C1E"
                      />
                    </Box>
                  </TouchableOpacity>
                </HStack>
              </Box>
            ))}

            {filteredData.length === 0 && (
              <Box
                bg={"white"}
                rounded={"2xl"}
                p={moderateScale(40)}
                alignItems={"center"}
                shadow={1}>
                <MaterialCommunityIcons
                  name="dialpad-search"
                  size={moderateScale(48)}
                  color="#9CA3AF"
                />
                <Text
                  fontFamily={"openSansBold"}
                  fontSize={moderateScale(16)}
                  color={"gray.500"}
                  mt={moderateScale(16)}
                  textAlign={"center"}>
                  No USSD codes found
                </Text>
                <Text
                  fontFamily={"openSansMedium"}
                  fontSize={moderateScale(14)}
                  color={"gray.400"}
                  mt={moderateScale(8)}
                  textAlign={"center"}>
                  Try searching with different keywords
                </Text>
              </Box>
            )}
          </VStack>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default UssdCodes;
