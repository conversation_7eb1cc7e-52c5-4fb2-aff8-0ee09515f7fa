import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  Box,
  Button,
  FormControl,
  HStack,
  ScrollView,
  StatusBar,
  Text,
  TextArea,
  VStack,
} from "native-base";
import React, { useState } from "react";
import { SafeAreaView, TouchableOpacity } from "react-native";
import { moderateScale } from "react-native-size-matters";

const ChangePassPhrase = ({ route }) => {
  const [oldPassphrase, setOldPassphrase] = useState("");
  const [errorOldPassphrase, setErrorOldPassphrase] = useState("");
  const [newPassphrase, setNewPassphrase] = useState("");
  const [errorNewPassphrase, setErrorNewPassphrase] = useState("");
  const [confirmPassphrase, setConfirmPassphrase] = useState("");
  const [errorConfirmPassphrase, setErrorConfirmPassphrase] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [failed, setFailed] = useState(false);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const navigation = useNavigation();

  const validate = () => {
    if (oldPassphrase === "") {
      setErrorOldPassphrase("Old Passphrase is required");
      return false;
    }
    if (newPassphrase === "") {
      setErrorNewPassphrase("New Passphrase is required");
      return false;
    }
    if (confirmPassphrase === "") {
      setErrorConfirmPassphrase("Confirm Passphrase is required");
      return false;
    }
    if (newPassphrase !== confirmPassphrase) {
      setErrorConfirmPassphrase(
        "New Passphrase and Confirm Passphrase do not match"
      );
      return false;
    }
    return true;
  };

  /*   const handleChangePassphrase = async () => {
    try {
      const res = validate();
      if (!res) {
        return;
      }
      setLoading(true);
      setFailed(false);

      const allData = {
        newPassphrase: newPassphrase,
        oldPassphrase: oldPassphrase,
        phoneNumber: route.params.phone,
      };

      const response = await Auth.changePassphrase(allData);
      if (response.data.success) {
        setSuccess(true);
        setSuccessMessage("Passphrase changed successfully");
        setTimeout(() => {
          navigation.navigate("Signin");
        }, 2000);
      } else {
        setFailed(true);
        setErrorMessage("Something went wrong, Please try again");
      }
      setLoading(false);
      setOldPassphrase("");
      setNewPassphrase("");
      setConfirmPassphrase("");
      setErrorMessage("");
    } catch (error) {
      setLoading(false);
      setFailed(true);
      if (error.response) {
        if (error.response.status == 400) {
          setErrorMessage("Something went wrong, Please try again");
        } else if (
          error.response.status == 403 ||
          error.response.status == 500 ||
          error.response.status == 504
        ) {
          setErrorMessage("Server Error, Please try again later");
        } else {
          if (error.response.data.message) {
            setErrorMessage(error.response.data.message);
          } else {
            setErrorMessage("Something went wrong, Please try again");
          }
        }
      } else if (error.request) {
        setErrorMessage("Network Error, Please check your internet connection");
      } else {
        setErrorMessage("Something went wrong, Please try again");
      }
    }
  }; */

  const handleChangePassphrase = () => {
    const isValid = validate();
    if (isValid) {
      // TODO: Implement passphrase change logic
      console.log("Change passphrase logic to be implemented");
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <StatusBar backgroundColor={"white"} barStyle="dark-content" />
      <ScrollView flex={1} bg={"gray.50"} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Box bg={"white"} px={moderateScale(20)} pt={moderateScale(10)}>
          <VStack space={moderateScale(16)} py={moderateScale(16)}>
            <HStack space={moderateScale(16)} alignItems={"center"}>
              <Box
                bg={"#F68C1E"}
                rounded={"full"}
                w={moderateScale(48)}
                h={moderateScale(48)}
                alignItems={"center"}
                justifyContent={"center"}>
                <MaterialCommunityIcons
                  name="form-textbox-password"
                  size={moderateScale(24)}
                  color="white"
                />
              </Box>
              <VStack flex={1}>
                <Text
                  fontFamily={"openSansBold"}
                  fontSize={moderateScale(28)}
                  color={"gray.900"}>
                  Change Passphrase
                </Text>
                <Text
                  fontFamily={"openSansMedium"}
                  fontSize={moderateScale(16)}
                  color={"gray.500"}>
                  Update your account passphrase
                </Text>
              </VStack>
            </HStack>
          </VStack>
        </Box>

        {/* Form */}
        <Box px={moderateScale(20)} py={moderateScale(20)}>
          <Box
            bg={"white"}
            rounded={"2xl"}
            p={moderateScale(24)}
            shadow={2}
            borderWidth={1}
            borderColor={"gray.100"}>
            <VStack space={moderateScale(20)}>
              {/* Success/Error Messages */}
              {success && (
                <Box
                  bg={"green.50"}
                  rounded={"xl"}
                  p={moderateScale(16)}
                  borderWidth={1}
                  borderColor={"green.200"}>
                  <HStack space={moderateScale(12)} alignItems={"center"}>
                    <MaterialCommunityIcons
                      name="check-circle"
                      size={moderateScale(20)}
                      color="#10B981"
                    />
                    <Text
                      fontFamily={"openSansMedium"}
                      fontSize={moderateScale(14)}
                      color={"green.700"}>
                      {successMessage}
                    </Text>
                  </HStack>
                </Box>
              )}

              {failed && (
                <Box
                  bg={"red.50"}
                  rounded={"xl"}
                  p={moderateScale(16)}
                  borderWidth={1}
                  borderColor={"red.200"}>
                  <HStack space={moderateScale(12)} alignItems={"center"}>
                    <MaterialCommunityIcons
                      name="alert-circle"
                      size={moderateScale(20)}
                      color="#EF4444"
                    />
                    <Text
                      fontFamily={"openSansMedium"}
                      fontSize={moderateScale(14)}
                      color={"red.700"}>
                      {errorMessage}
                    </Text>
                  </HStack>
                </Box>
              )}

              {/* Old Passphrase */}
              <FormControl isInvalid={!!errorOldPassphrase}>
                <VStack space={moderateScale(8)}>
                  <Text
                    fontFamily={"openSansBold"}
                    fontSize={moderateScale(16)}
                    color={"gray.900"}>
                    Current Passphrase
                  </Text>
                  <TextArea
                    value={oldPassphrase}
                    fontFamily={"openSansMedium"}
                    onFocus={() => setErrorOldPassphrase("")}
                    focusOutlineColor={"#F68C1E"}
                    borderColor={errorOldPassphrase ? "red.400" : "gray.200"}
                    placeholder="Enter your current passphrase"
                    fontSize={moderateScale(14)}
                    bg={"gray.50"}
                    rounded={"xl"}
                    onChangeText={(text) => setOldPassphrase(text)}
                    h={moderateScale(80)}
                    keyboardType={"default"}
                    _focus={{
                      bg: "white",
                      borderColor: "#F68C1E",
                    }}
                  />
                  {errorOldPassphrase && (
                    <Text
                      fontFamily={"openSansMedium"}
                      color={"red.500"}
                      fontSize={moderateScale(12)}>
                      {errorOldPassphrase}
                    </Text>
                  )}
                </VStack>
              </FormControl>

              {/* New Passphrase */}
              <FormControl isInvalid={!!errorNewPassphrase}>
                <VStack space={moderateScale(8)}>
                  <Text
                    fontFamily={"openSansBold"}
                    fontSize={moderateScale(16)}
                    color={"gray.900"}>
                    New Passphrase
                  </Text>
                  <TextArea
                    value={newPassphrase}
                    fontFamily={"openSansMedium"}
                    onFocus={() => setErrorNewPassphrase("")}
                    focusOutlineColor={"#F68C1E"}
                    borderColor={errorNewPassphrase ? "red.400" : "gray.200"}
                    placeholder="Enter your new passphrase"
                    fontSize={moderateScale(14)}
                    bg={"gray.50"}
                    rounded={"xl"}
                    onChangeText={(text) => setNewPassphrase(text)}
                    h={moderateScale(80)}
                    keyboardType={"default"}
                    _focus={{
                      bg: "white",
                      borderColor: "#F68C1E",
                    }}
                  />
                  {errorNewPassphrase && (
                    <Text
                      fontFamily={"openSansMedium"}
                      color={"red.500"}
                      fontSize={moderateScale(12)}>
                      {errorNewPassphrase}
                    </Text>
                  )}
                </VStack>
              </FormControl>

              {/* Confirm Passphrase */}
              <FormControl isInvalid={!!errorConfirmPassphrase}>
                <VStack space={moderateScale(8)}>
                  <Text
                    fontFamily={"openSansBold"}
                    fontSize={moderateScale(16)}
                    color={"gray.900"}>
                    Confirm New Passphrase
                  </Text>
                  <TextArea
                    value={confirmPassphrase}
                    fontFamily={"openSansMedium"}
                    onFocus={() => setErrorConfirmPassphrase("")}
                    focusOutlineColor={"#F68C1E"}
                    borderColor={
                      errorConfirmPassphrase ? "red.400" : "gray.200"
                    }
                    placeholder="Confirm your new passphrase"
                    fontSize={moderateScale(14)}
                    bg={"gray.50"}
                    rounded={"xl"}
                    onChangeText={(text) => setConfirmPassphrase(text)}
                    h={moderateScale(80)}
                    keyboardType={"default"}
                    _focus={{
                      bg: "white",
                      borderColor: "#F68C1E",
                    }}
                  />
                  {errorConfirmPassphrase && (
                    <Text
                      fontFamily={"openSansMedium"}
                      color={"red.500"}
                      fontSize={moderateScale(12)}>
                      {errorConfirmPassphrase}
                    </Text>
                  )}
                </VStack>
              </FormControl>

              {/* Submit Button */}
              <TouchableOpacity
                onPress={handleChangePassphrase}
                disabled={loading}>
                <Box
                  bg={loading ? "gray.300" : "#F68C1E"}
                  rounded={"xl"}
                  py={moderateScale(16)}
                  alignItems={"center"}
                  shadow={loading ? 0 : 2}>
                  <HStack space={moderateScale(8)} alignItems={"center"}>
                    {loading && (
                      <MaterialCommunityIcons
                        name="loading"
                        size={moderateScale(20)}
                        color="white"
                      />
                    )}
                    <Text
                      fontFamily={"openSansBold"}
                      fontSize={moderateScale(16)}
                      color={"white"}>
                      {loading ? "Changing..." : "Change Passphrase"}
                    </Text>
                  </HStack>
                </Box>
              </TouchableOpacity>
            </VStack>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ChangePassPhrase;
