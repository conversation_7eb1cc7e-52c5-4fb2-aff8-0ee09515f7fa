import { extendTheme } from "native-base";

const theme = extendTheme({
  colors: {
    // NetOne Brand Colors
    brand: {
      // Primary brand orange
      50: "#fff7ed",
      100: "#ffedd5",
      200: "#fed7aa",
      300: "#fdba74",
      400: "#fb923c",
      500: "#FF8500", // Main brand orange
      600: "#ea580c",
      700: "#c2410c",
      800: "#9a3412",
      900: "#7c2d12",
    },
    // Dark brand colors
    dark: {
      50: "#f8f9fa",
      100: "#e9ecef",
      200: "#dee2e6",
      300: "#ced4da",
      400: "#adb5bd",
      500: "#6c757d",
      600: "#495057",
      700: "#343a40",
      800: "#241F20", // Dark gray
      900: "#0E0C1D", // Primary dark
    },
    // Light brand colors
    light: {
      50: "#ffffff",
      100: "#f8f9fa",
      200: "#E5E6E8", // Light gray
      300: "#dee2e6",
      400: "#ced4da",
      500: "#adb5bd",
      600: "#6c757d",
      700: "#495057",
      800: "#343a40",
      900: "#212529",
    },
    // Override primary to use brand orange
    primary: {
      50: "#fff7ed",
      100: "#ffedd5",
      200: "#fed7aa",
      300: "#fdba74",
      400: "#fb923c",
      500: "#FF8500",
      600: "#ea580c",
      700: "#c2410c",
      800: "#9a3412",
      900: "#7c2d12",
    },
  },
});

export default theme;
