import React from "react";
import { View, Text, Center, Spinner } from "native-base";
import { moderateScale } from "react-native-size-matters";

const AuthLoadingScreen = () => {
  return (
    <View flex={1} bg="white" justifyContent="center" alignItems="center">
      <Center>
        <Spinner
          size="lg"
          color="#F68C1E"
          accessibilityLabel="Loading authentication"
        />
        <Text
          mt={moderateScale(16)}
          fontSize={moderateScale(16)}
          fontFamily="openSansMedium"
          color="gray.600">
          Checking authentication
        </Text>
      </Center>
    </View>
  );
};

export default AuthLoadingScreen;
