import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import {
  Box,
  Button,
  HStack,
  Pressable,
  Text,
  View,
  IconButton,
} from "native-base";
import { moderateScale } from "react-native-size-matters";
import { Octicons, Ionicons } from "@expo/vector-icons";
import HomeScreen from "../screens/Home";
import { SafeAreaView, Alert, Platform, StatusBar } from "react-native";
import SignIn from "../screens/SignIn";
import SignUp from "../screens/SignUp";
import PhoneNumber from "../screens/PhoneNumber";
import Landing from "../screens/Landing";
import Main from "../screens/Main";
import LocalStore from "../utilities/store";
import { useEffect, useState } from "react";
import { navigationref, isreadyref, navigate } from "./rootNav";
import LinkedNumbers from "../screens/LinkedNumber/LinkNumbers";
import OneMoneyPin from "../screens/Pins/OneMoneyPin";
import RequestPuk from "../screens/Puk/RequestPuk";
import UssdCodes from "../screens/Info/UssdCodes";
import ChangePassPhrase from "../screens/Pins/Passphrase";
import ChangePassword from "../screens/Pins/Password";
import AirtimeBundle from "../screens/AirtimeBundles/airtimeBundle";
import ConfirmAirtimeBundle from "../screens/AirtimeBundles/confirmAirtimeBundle";
import Tabs from "./bottomTabs";
import Zec from "../screens/Services/Zec";
import SuccessSendPage from "../screens/SuccessPages/SuccessPage";
import OTP from "../screens/Otp";
import LoginOTP from "../screens/LoginOtp";
import CreditTransfer from "../screens/CreditTransfer";
import ConfirmAirtimeRecharge from "../screens/AirtimeRecharge/confirmAirtimeRecharge";
import ConfirmCreditTransfer from "../screens/CreditTransfer/confirmCreditTransfer";
import Shops from "../screens/NetoneShops";
import Contact from "../screens/Account/contact";
import SetForgotPassword from "../screens/SetForgotPassword";
import ConfirmAirtime from "../screens/AirtimeOnemoney/confirmAirtime";
import Airtime from "../screens/AirtimeOnemoney/airtime";
import AirtimeRecharge from "../screens/AirtimeRecharge/airtimeRecharge";
import AirtimePurchase from "../screens/AirtimeRecharge";
import { useAuth } from "../utilities/AuthContext";
import AuthLoadingScreen from "../components/AuthLoadingScreen";
import FAQ from "../screens/FAQ/faq";

const Stack = createNativeStackNavigator();

// Custom header component with back icon
const CustomHeader = ({ navigation, title, canGoBack }) => {
  return (
    <>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="white"
        translucent={false}
      />
      <SafeAreaView style={{ backgroundColor: "white" }}>
        <Box
          bgColor={"white"}
          w={"full"}
          h={moderateScale(60)}
          px={moderateScale(15)}
          borderBottomWidth={0}
          shadow={0}>
          <HStack
            h={"full"}
            alignItems={"center"}
            justifyContent={"space-between"}>
            <HStack alignItems={"center"} space={3}>
              {canGoBack && (
                <IconButton
                  icon={
                    <Ionicons
                      name="arrow-back"
                      size={moderateScale(24)}
                      color="#333"
                    />
                  }
                  onPress={() => navigation.goBack()}
                  _pressed={{ opacity: 0.5 }}
                />
              )}
              <Text fontSize={moderateScale(18)} fontWeight="600" color="#333">
                {title}
              </Text>
            </HStack>
          </HStack>
        </Box>
      </SafeAreaView>
    </>
  );
};

function Navigation() {
  const { isAuthenticated, initialLoading } = useAuth();

  if (initialLoading) {
    return <AuthLoadingScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName={isAuthenticated ? "Tabs" : "Landing"}
        screenOptions={({ navigation, route }) => ({
          contentStyle: {
            display: "flex",
            flexGrow: 1,
            backgroundColor: "white",
          },
          headerStyle: {
            elevation: 0,
            shadowOpacity: 0,
            backgroundColor: "white",
            borderBottomWidth: 0,
          },
          headerShadowVisible: false,
          presentation: Platform.OS == "android" ? "modal" : "card",
          animationTypeForReplace: "push",
          animation: "slide_from_right",
          orientation: "portrait",
          header: ({ options }) => (
            <CustomHeader
              navigation={navigation}
              title={options.title || ""}
              canGoBack={navigation.canGoBack()}
            />
          ),
        })}>
        <Stack.Screen
          options={{
            title: "",
            headerShown: false,
          }}
          name="Landing"
          component={Landing}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: true,
            /*    header: ({}) => {
              return (
                <SafeAreaView>
                  <Box
                    bgColor={"#F68C1E"}
                    w={"full"}
                    h={moderateScale(60)}
                    px={moderateScale(15)}>
                    <HStack
                      h={"full"}
                      alignItems={"center"}
                      justifyContent={"space-between"}>
                      <Text color={"white"}>O784675999</Text>
                      <Button rounded={"full"}>
                        <Octicons
                          name="sign-out"
                          color={"white"}
                          size={moderateScale(20)}></Octicons>
                      </Button>
                    </HStack>
                  </Box>
                </SafeAreaView>
              );
            }, */
          }}
          name="Home"
          component={HomeScreen}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: true,
          }}
          name="SignIn"
          component={SignIn}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: true,
          }}
          name="Login"
          component={SignIn}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: false,
          }}
          name="Tabs"
          component={Tabs}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: true,
          }}
          name="SignUp"
          component={SignUp}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: true,
          }}
          name="otp"
          component={OTP}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: true,
          }}
          name="LoginOtp"
          component={LoginOTP}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: true,
          }}
          name="phoneNumber"
          component={PhoneNumber}
        />

        <Stack.Screen
          options={{
            title: "Linked Numbers",
            headerShown: true,
          }}
          name="LinkedNumbers"
          component={LinkedNumbers}
        />
        <Stack.Screen
          options={{
            title: "Change OneMoney Pin",
            headerShown: true,
          }}
          name="oneMoneyPin"
          component={OneMoneyPin}
        />
        <Stack.Screen
          options={{
            title: "Request Puk",
            headerShown: true,
          }}
          name="requestPuk"
          component={RequestPuk}
        />
        <Stack.Screen
          options={{
            title: "Ussd Codes",
            headerShown: true,
          }}
          name="ussdCodes"
          component={UssdCodes}
        />
        <Stack.Screen
          options={{
            title: "Change Passphrase",
            headerShown: true,
          }}
          name="passphrase"
          component={ChangePassPhrase}
        />
        <Stack.Screen
          options={{
            title: "Change Password",
            headerShown: true,
          }}
          name="password"
          component={ChangePassword}
        />
        <Stack.Screen
          options={{
            title: "Set Password",
            headerShown: true,
          }}
          name="SetForgotPassword"
          component={SetForgotPassword}
        />
        <Stack.Screen
          options={{
            title: "Buy Bundles",
            headerShown: true,
          }}
          name="airtimeBundle"
          component={AirtimeBundle}
        />
        <Stack.Screen
          options={{
            title: "Transfer Credit",
            headerShown: true,
          }}
          name="creditTransfer"
          component={CreditTransfer}
        />
        <Stack.Screen
          options={{
            title: "Airtime Recharge",
            headerShown: true,
          }}
          name="airtimeRecharge"
          component={AirtimeRecharge}
        />
        <Stack.Screen
          options={{
            title: "Airtime Purchase",
            headerShown: true,
          }}
          name="airtimePurchase"
          component={AirtimePurchase}
        />
        <Stack.Screen
          options={{
            title: "Confirm Bundle Purchase",
            headerShown: true,
          }}
          name="confirmAirtimeBundle"
          component={ConfirmAirtimeBundle}
        />

        <Stack.Screen
          options={{
            title: "Confirm Airtime Recharge",
            headerShown: true,
          }}
          name="confirmAirtimeRecharge"
          component={ConfirmAirtimeRecharge}
        />

        <Stack.Screen
          options={{
            title: "Confirm Credit Transfer",
            headerShown: true,
          }}
          name="confirmCreditTransfer"
          component={ConfirmCreditTransfer}
        />
        <Stack.Screen
          options={{
            title: "",
            headerShown: false,
          }}
          name="SuccessSend"
          component={SuccessSendPage}
        />
        <Stack.Screen
          options={{
            title: "Zec Registration Details",
            headerShown: true,
          }}
          name="zec"
          component={Zec}
        />
        <Stack.Screen
          options={{
            title: "Netone Shops",
            headerShown: true,
          }}
          name="shops"
          component={Shops}
        />
        <Stack.Screen
          options={{
            title: "Contact Us",
            headerShown: true,
          }}
          name="contact"
          component={Contact}
        />
        <Stack.Screen
          options={{
            title: "FAQ",
            headerShown: true,
          }}
          name="faq"
          component={FAQ}
        />
        <Stack.Screen
          options={({ route }) => ({
            headerShown: true,
            title: "Confirm Airtime Purchase",
          })}
          name="ConfirmAirtime"
          component={ConfirmAirtime}
        />
        <Stack.Screen
          options={({ route }) => ({
            title: "Buy Airtime With OneMoney",
          })}
          name="Airtime"
          component={Airtime}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default Navigation;
