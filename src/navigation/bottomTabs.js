import {
  Entypo,
  FontAwesome,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { useNavigation } from "@react-navigation/native";
import { useEffect, useState } from "react";
import { Alert, Platform } from "react-native";
import { moderateVerticalScale } from "react-native-size-matters";
import Svg, { Path } from "react-native-svg";
import LocalStore from "../utilities/store";
import Main4 from "../screens/Main";
import { navigationref, isreadyref, navigate } from "./rootNav";

import Account from "../screens/Account";
import SelfCare from "../screens/SelfCare";
import { useAuth } from "../utilities/AuthContext";

const Tab = createBottomTabNavigator();

function Tabs() {
  const navigation = useNavigation();
  const { logout } = useAuth();
  const [logoutClicked, setLogoutClicked] = useState(false);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    return () => {
      isreadyref.current = false;
    };
  }, []);

  const handleLogout = async () => {
    setLogoutClicked(true);
    const success = await logout();

    // Clear additional data
    await LocalStore.deleteData("@bundles");

    if (success) {
      navigation.navigate("Landing");
    } else {
      console.log("logout failed");
      setLogoutClicked(false);
    }
  };

  return (
    <>
      <Tab.Navigator
        onready={() => {
          isreadyref.current = true;
        }}
        initialRouteName="Main"
        screenOptions={{
          tabBarShowLabel: true,
          tabBarLabelStyle: {
            fontSize: moderateVerticalScale(12),
            fontFamily: "openSansSemiBold",
            marginBottom: 4,
          },
          tabBarStyle: {
            backgroundColor: "white",
            height:
              Platform.OS === "android"
                ? moderateVerticalScale(65)
                : moderateVerticalScale(85),
            paddingTop: 8,
            paddingBottom: Platform.OS === "android" ? 8 : 20,
            borderTopWidth: 1,
            borderTopColor: "#E5E7EB",
            elevation: 8,
            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: -2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 8,
          },
          tabBarIconStyle: {
            marginBottom: 2,
          },
        }}>
        <Tab.Screen
          listeners={{
            beforeRemove: (e) => {
              if (logoutClicked === false) {
                e.preventDefault();
                Alert.alert(
                  "Logging Out",
                  "Do you want to logout ?",
                  [
                    {
                      text: "No",
                      style: "cancel",
                    },
                    { text: "Yes", onPress: () => handleLogout() },
                  ],
                  { cancelable: false }
                );
              }
            },
          }}
          options={{
            headerShown: false,
            tabBarLabel: "Home",
            tabBarIcon: ({ color, size }) => (
              <Svg width={size} height={size} viewBox="0 0 24 24">
                <Path
                  fill="none"
                  stroke={color}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="m3.164 11.35l.836.209l.457 4.542c.258 2.566.387 3.849 1.244 4.624s2.147.775 4.726.775h3.146c2.58 0 3.87 0 4.726-.775c.857-.775.986-2.058 1.244-4.625L20 11.56l.836-.21a1.537 1.537 0 0 0 .509-2.75l-8.198-5.737a2 2 0 0 0-2.294 0L2.655 8.6a1.537 1.537 0 0 0 .51 2.75"
                />
              </Svg>
            ),
            tabBarActiveTintColor: "#FF8500",
            tabBarInactiveTintColor: "#737373",
          }}
          name="Main"
          component={Main4}
          initialParams={{ current: "Main" }}
        />

        <Tab.Screen
          options={{
            headerShown: false,
            tabBarLabel: "Self Service",
            tabBarIcon: ({ color, size }) => (
              <Svg width={size} height={size} viewBox="0 0 24 24">
                <Path
                  fill="none"
                  stroke={color}
                  strokeWidth={2}
                  d="M18.152 11.355a3.176 3.176 0 0 1 0 4.492l-.01.01L12 22l-6.144-6.144l-.009-.009a3.173 3.173 0 1 1 4.648-4.314L12 13.039l1.45-1.45a3.176 3.176 0 0 1 4.703-.233ZM8.999 5a3 3 0 1 0 6 0a3 3 0 1 0-6 0"
                />
              </Svg>
            ),
            tabBarActiveTintColor: "#FF8500",
            tabBarInactiveTintColor: "#737373",
          }}
          name="SelfCare"
          component={SelfCare}
        />

        <Tab.Screen
          options={{
            headerShown: false,
            tabBarLabel: "Account",
            tabBarIcon: ({ color, size }) => (
              <Svg width={size} height={size} viewBox="0 0 24 24">
                <Path
                  fill="none"
                  stroke={color}
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 20.75a1 1 0 0 0 1-1v-1.246c.004-2.806-3.974-5.004-8-5.004s-8 2.198-8 5.004v1.246a1 1 0 0 0 1 1zM15.604 6.854a3.604 3.604 0 1 1-7.208 0a3.604 3.604 0 0 1 7.208 0"
                />
              </Svg>
            ),
            tabBarActiveTintColor: "#FF8500",
            tabBarInactiveTintColor: "#737373",
          }}
          name="Account"
          component={Account}
        />
      </Tab.Navigator>
    </>
  );
}

export default Tabs;
