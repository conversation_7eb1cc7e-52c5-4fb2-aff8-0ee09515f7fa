import { StatusBar } from "expo-status-bar";
import { Feather } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import Navigation from "./src/navigation/Stack";
import { <PERSON><PERSON>, HStack, NativeBaseProvider, Text, View } from "native-base";
import { useFonts } from "expo-font";
import theme from "./src/utilities/theme";
import NetInfo from "@react-native-community/netinfo";
import { RootSiblingParent } from "react-native-root-siblings";
import { AuthProvider } from "./src/utilities/AuthContext";

export default function App() {
  const [connected, setConnected] = useState(true);
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (!state.isConnected) {
        setConnected(false);
      } else {
        setConnected(true);
      }
    });
    return () => {
      unsubscribe();
    };
  }, []);

  const [loaded] = useFonts({
    openSans: require("./src/assets/fonts/open.ttf"),
    openSansBold: require("./src/assets/fonts/OpenSans-Bold.ttf"),
    openSansExtraBold: require("./src/assets/fonts/OpenSans-ExtraBold.ttf"),
    openSansSemiBold: require("./src/assets/fonts/OpenSans-SemiBold.ttf"),
    openSansMedium: require("./src/assets/fonts/OpenSans-Medium.ttf"),
  });

  if (!loaded) {
    return null;
  }

  return (
    <AuthProvider>
      <RootSiblingParent>
        <NativeBaseProvider theme={theme}>
          <View
            style={{
              flex: 1,
              backgroundColor: "#FF8500",
            }}>
            <StatusBar style="dark" backgroundColor="#FF8500" />
            <Navigation />
            {!connected && (
              <Alert w="100%" status={"warning"}>
                <HStack space={4}>
                  <Feather name="wifi-off" size={24} color="black" />
                  <Text fontSize="md" color="coolGray.800">
                    Poor internet connection
                  </Text>
                </HStack>
              </Alert>
            )}
          </View>
        </NativeBaseProvider>
      </RootSiblingParent>
    </AuthProvider>
  );
}
