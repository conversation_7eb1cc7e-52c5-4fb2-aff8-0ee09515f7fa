{"expo": {"name": "Netone", "slug": "Netone", "version": "1.0.0", "orientation": "portrait", "jsEngine": "hermes", "assetBundlePatterns": ["**/*", "assets/fonts/*"], "icon": "./src/assets/Icons/NetoneIcon.png", "splash": {"image": "./src/assets/SplashScreen/splash.png", "resizeMode": "cover", "backgroundColor": "#fff"}, "android": {"adaptiveIcon": {"foregroundImage": "./src/assets/Icons/NetoneAdaptiveIcon.png", "backgroundColor": "#fff"}, "permissions": ["android.permission.READ_CONTACTS", "android.permission.WRITE_CONTACTS"], "package": "com.emacliam.netone"}, "plugins": [["expo-contacts", {"contactsPermission": "Allow $(PRODUCT_NAME) to access your contacts."}], ["expo-build-properties", {"android": {"usesCleartextTraffic": true}}], "expo-font"], "extra": {"eas": {"projectId": "5545f4a7-7aed-4ec5-8db1-a191cc950a2e"}}, "ios": {"infoPlist": {"NSContactsUsageDescription": "Allow $(PRODUCT_NAME) to access your contacts.", "ITSAppUsesNonExemptEncryption": false}, "bundleIdentifier": "com.emacliam.netone"}}}